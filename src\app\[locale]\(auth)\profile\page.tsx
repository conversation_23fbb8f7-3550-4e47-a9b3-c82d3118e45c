import { auth } from "@/lib/auth/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { linksObj } from "@/app/links";
import UserProfileCard from "@/components/UserProfileCard";
import OrdersList from "@/components/OrdersList";
import { Suspense } from "react";
import { Loader2 } from "lucide-react";

const ProfilePage = async () => {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user.id) {
    redirect(linksObj.signOn.href);
  }

  const user = {
    name: session.user.name || "",
    email: session.user.email || "",
    phoneNumber: session.user.phoneNumber,
  };

  return (
    <main className="container-full flex min-h-[80vh] flex-col">
      <section className="mx-auto grid w-full max-w-xl gap-8">
        <UserProfileCard user={user} />
        <Suspense
          fallback={
            <div className="mx-auto flex h-96 items-center justify-center">
              <Loader2 className="size-6 animate-spin" />
            </div>
          }
        >
          <OrdersList userId={session.user.id} />
        </Suspense>
      </section>
    </main>
  );
};

export default ProfilePage;
