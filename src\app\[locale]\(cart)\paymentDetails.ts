import { paymentMethods } from "./model";
import { PAYMENT_COORDINATES } from "./paymentConstants";

export type PaymentDetail = {
  label: string;
  value?: string;
};

export type PaymentDetailsMap = {
  [key in keyof typeof paymentMethods]: PaymentDetail[];
};

/**
 * Get payment details for a specific payment method
 * @param t - Translation function from useTranslations or getTranslations
 * @param paymentMethod - The payment method to get details for
 * @returns Array of payment details
 */
export function getPaymentDetails(
  t: (key: any) => string,
  paymentMethod: keyof typeof paymentMethods,
): PaymentDetail[] {
  const paymentDetailsMap: PaymentDetailsMap = {
    [paymentMethods.BANK_TRANSFER]: [
      {
        label: t("bankTransferDetails.entityLabel"),
        value: PAYMENT_COORDINATES.entity,
      },
      {
        label: t("bankTransferDetails.nifLabel"),
        value: PAYMENT_COORDINATES.nif,
      },
      {
        label: t("bankTransferDetails.bankNameLabel"),
        value: PAYMENT_COORDINATES.bank,
      },
      {
        label: t("bankTransferDetails.accountNumberLabel"),
        value: PAYMENT_COORDINATES.account,
      },
      {
        label: t("bankTransferDetails.ibanLabel"),
        value: PAYMENT_COORDINATES.iban,
      },
      {
        label: t("bankTransferDetails.confirmationNote"),
      },
      {
        label: t("bankTransferDetails.paymentDeadlineNote"),
      },
    ],
    [paymentMethods.MULTICAIXA_EXPRESS]: [
      {
        label: t("multicaixaExpressDetails.numberLabel"),
        value: "9XX XXX XXX",
      },
      {
        label: t("multicaixaExpressDetails.paymentDeadlineNote"),
      },
    ],
    [paymentMethods.PAY_IN_STORE]: [
      {
        label: t("payInStoreDetails.confirmationNote"),
      },
      {
        label: t("payInStoreDetails.paymentDeadlineNote"),
      },
    ],
  };

  return paymentDetailsMap[paymentMethod] || [];
}

/**
 * Get all payment details mapped by payment method
 * @param t - Translation function from useTranslations or getTranslations
 * @returns Complete payment details map
 */
export function getAllPaymentDetails(
  t: (key: any) => string,
): PaymentDetailsMap {
  return {
    [paymentMethods.BANK_TRANSFER]: getPaymentDetails(
      t,
      paymentMethods.BANK_TRANSFER,
    ),
    [paymentMethods.MULTICAIXA_EXPRESS]: getPaymentDetails(
      t,
      paymentMethods.MULTICAIXA_EXPRESS,
    ),
    [paymentMethods.PAY_IN_STORE]: getPaymentDetails(
      t,
      paymentMethods.PAY_IN_STORE,
    ),
  };
}
