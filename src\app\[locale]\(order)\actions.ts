import prisma from "@/lib/prisma";
import { orderSchema, OrderType } from "./model";
import { z } from "zod";

export const getUserOrdersById = async (
  userId: string,
): Promise<OrderType[]> => {
  try {
    const orders = await prisma.order.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return z.array(orderSchema).parse(orders);
  } catch (error) {
    console.error("Error fetching user orders:", error);
    return [];
  }
};
