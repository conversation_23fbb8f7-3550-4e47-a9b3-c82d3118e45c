import { SlideAnim } from "@/app/animations/Animations";
import sneakersTopBottom from "@/assets/imgs/sneakers-top-bottom.png";
import sneakerSideLeft from "@/assets/imgs/sneaker-side-left.png";
import sneakerSideRightRotate from "@/assets/imgs/sneaker-side-right-rotate.png";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { getTranslations } from "next-intl/server";
import Image from "next/image";
import { SizeSelector } from "@/components/SizeSelector";
import Text, { textVariants } from "@/components/Text";

import { availableSizes } from "./data";
import { formatKwanza } from "@/lib/currency";
import CartActionButton from "./(cart)/CartActionButton";

type Props = {
  className?: string;
};

const ProductDetailsSection = async ({ className }: Props) => {
  const t = await getTranslations("ProductDetails");

  const productDetails = [
    t("detailsList.item1"),
    t("detailsList.item2"),
    t("detailsList.item3"),
    t("detailsList.item4"),
    t("detailsList.item5"),
    t("detailsList.item6"),
    t("detailsList.item7"),
    t("detailsList.item8"),
    t("detailsList.item9"),
    t("detailsList.item10"),
  ];

  const careInstructions = [
    t("careList.item1"),
    t("careList.item2"),
    t("careList.item3"),
    t("careList.item4"),
    t("careList.item5"),
    t("careList.item6"),
  ];

  const details = [productDetails, careInstructions];

  const images = [
    {
      src: sneakerSideLeft,
      alt: "Sneaker side left view",
    },
    {
      src: sneakerSideRightRotate,
      alt: "Sneaker side right rotated view",
    },
    {
      src: sneakersTopBottom,
      alt: "Sneaker top and bottom view",
    },
  ];

  return (
    <section className="w-full bg-white py-8">
      <div
        className={cn(
          "grid grid-cols-1 gap-10 md:grid-cols-2 lg:gap-20",
          className,
        )}
      >
        <SlideAnim
          direction="up"
          stagger
          selector="img"
          options={{ delay: 0.5 }}
        >
          <article className="flex flex-col gap-4 max-md:order-1">
            {images.map((image, index) => (
              <Image
                src={image.src}
                alt={image.alt}
                width={image.src.width}
                height={image.src.height}
                key={index}
                placeholder="blur"
                className="z-10 h-auto max-h-[80vh] w-full object-contain last-of-type:-my-40 max-md:last-of-type:-mt-20 md:w-[80%]"
              />
            ))}
          </article>
        </SlideAnim>
        <SlideAnim direction="down" options={{ duration: 0.7 }}>
          <article id="product" className="flex flex-col gap-5">
            <span className="flex flex-col gap-1">
              <Text as="h2" size="2xl" className="font-semibold">
                {t("productName")}
              </Text>
              <Text as="h4" size="lg">
                {t("productId")}
              </Text>
            </span>
            <Text size="lg" className="font-extrabold">
              {formatKwanza(parseInt(t("price")))}
            </Text>
            <div className="flex w-full max-w-sm flex-col gap-2.5">
              <SizeSelector sizes={availableSizes} />
              <span className="flex flex-wrap gap-4">
                <CartActionButton
                  type="add-to-cart"
                  className="flex-1 rounded-md"
                />
                <CartActionButton
                  type="buy-now"
                  className="max-xs:flex-1 rounded-md"
                />
              </span>
            </div>
            <div>
              {details.map((detail, index) => (
                <Accordion key={index} type="multiple">
                  <AccordionItem value={`item-${index + 1}`}>
                    <AccordionTrigger className="rounded-t-none border-t-2">
                      {index === 0 ? t("detailsTitle") : t("careTitle")}
                    </AccordionTrigger>
                    <AccordionContent className="">
                      <ul className="list-inside list-disc">
                        {detail.map((item, index2) => (
                          <li
                            className={cn(
                              textVariants({ size: "sm" }),
                              index === 0 &&
                                "lowercase last-of-type:mt-4 last-of-type:list-none",
                              "font-medium",
                            )}
                            key={index2}
                          >
                            {item}
                          </li>
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              ))}
            </div>
          </article>
        </SlideAnim>
      </div>
    </section>
  );
};

export default ProductDetailsSection;
