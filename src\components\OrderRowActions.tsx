"use client";

import { useState } from "react";
import { MoreVertical, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { OrderType } from "@/app/[locale]/(order)/model";
import { useTranslations } from "next-intl";
import OrderDetailsDialog from "./OrderDetailsDialog";

type OrderRowActionsProps = {
  order: OrderType;
};

export default function OrderRowActions({ order }: OrderRowActionsProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const t = useTranslations("Orders");

  const handleViewDetails = () => {
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
            <span className="sr-only">{t("openMenu")}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handleViewDetails}>
            <Eye className="mr-2 h-4 w-4" />
            {t("viewDetails")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <OrderDetailsDialog
        order={order}
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
      />
    </>
  );
}
