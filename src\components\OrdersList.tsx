import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Package, Calendar, MapPin, CreditCard } from "lucide-react";
import Text from "@/components/Text";
import { Badge } from "@/components/ui/badge";
import { format<PERSON>wan<PERSON> } from "@/lib/currency";
import { OrderType } from "@/app/[locale]/(order)/model";
import { getTranslations } from "next-intl/server";
import { getUserOrdersById } from "@/app/[locale]/(order)/actions";

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
} satisfies Record<OrderType["status"], string>;
type OrdersListProps = {
  userId: string;
  className?: string;
};

export default async function OrdersList({
  userId,
  className,
}: OrdersListProps) {
  const orders = await getUserOrdersById(userId);
  const t = await getTranslations("Orders");
  const tPaymentMethods = await getTranslations("CheckoutForm.paymentMethods");

  if (orders.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Package className="text-muted-foreground mb-4 h-12 w-12" />
          <Text as="h3" size="lg" className="mb-2 font-semibold">
            {t("noOrders")}
          </Text>
          <Text as="p" size="sm" className="text-muted-foreground text-center">
            {t("noOrdersDescription")}
          </Text>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="text-xl">{t("yourOrders")}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-4">
        {orders.map((order) => (
          <Card key={order.id}>
            <CardHeader className="gap-4">
              <div className="flex flex-wrap items-center justify-between gap-2">
                <CardTitle className="text-base">
                  {t("orderNumber")}: {order.id}
                </CardTitle>
                <Badge
                  className={cn(
                    "text-xs font-semibold",
                    statusColors[order.status],
                  )}
                >
                  {t(`status.${order.status}`)}
                </Badge>
              </div>
              <div className="text-muted-foreground flex flex-wrap items-center gap-2 text-xs sm:text-sm">
                <div className="flex items-center gap-1">
                  <Calendar className="size-4" />
                  {new Date(order.createdAt).toLocaleDateString()}
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="size-4" />
                  {order.storeName}
                </div>
                <div className="flex items-center gap-1">
                  <CreditCard className="size-4" />
                  {tPaymentMethods(order.paymentMethod)}
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              <ul className="space-y-2">
                {order.items &&
                  order.items.map((item, index) => (
                    <li
                      key={index}
                      className="flex items-center gap-2 border-b py-2 last:border-b-0 max-sm:flex-col max-sm:items-start sm:justify-between"
                    >
                      <div className="flex-1">
                        <Text as="p" size="sm" className="font-medium">
                          {item.name}
                        </Text>
                        <Text
                          as="p"
                          size="xs"
                          className="text-muted-foreground"
                        >
                          {t("size")}: {item.size.euSize} • {t("quantity")}:{" "}
                          {item.quantity}
                        </Text>
                      </div>
                      <Text as="p" size="xs" className="font-medium">
                        {formatKwanza(item.price)}
                      </Text>
                    </li>
                  ))}
              </ul>

              <div className="flex items-center justify-between border-t pt-2 font-semibold">
                <Text as="span" size="sm">
                  {t("total")}:
                </Text>
                <Text as="span" size="sm">
                  {formatKwanza(order.total)}
                </Text>
              </div>

              <Text as="p" size="sm" className="text-secondary">
                {t("pickupLocation")}: {order.storeAddress}
              </Text>
            </CardContent>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
}
