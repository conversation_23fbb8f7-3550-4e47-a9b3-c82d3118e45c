"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  Package,
  Calendar,
  MapPin,
  CreditCard,
  MoreVertical,
  Eye,
} from "lucide-react";
import Text from "@/components/Text";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { formatKwanza } from "@/lib/currency";
import { OrderType } from "@/app/[locale]/(order)/model";
import { useTranslations } from "next-intl";
import { getUserOrdersById } from "@/app/[locale]/(order)/actions";
import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { paymentMethods } from "@/app/[locale]/(cart)/model";
import {
  PAYMENT_COORDINATES,
  CONTACT_INFO,
} from "@/app/[locale]/(cart)/paymentConstants";

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
} satisfies Record<OrderType["status"], string>;

type OrdersListProps = {
  userId: string;
  className?: string;
};

type OrderDetailsDialogProps = {
  order: OrderType;
  isOpen: boolean;
  onClose: () => void;
};

function OrderDetailsDialog({
  order,
  isOpen,
  onClose,
}: OrderDetailsDialogProps) {
  const t = useTranslations("Orders");
  const tPaymentMethods = useTranslations("CheckoutForm.paymentMethods");
  const tPaymentDetails = useTranslations("CheckoutForm.paymentDetails");

  const getPaymentInstructions = () => {
    if (order.paymentMethod === paymentMethods.PAY_IN_STORE) {
      return (
        <div className="bg-muted rounded-lg p-4">
          <Text as="h4" size="sm" className="mb-2 font-semibold">
            {t("paymentInstructions")}
          </Text>
          <div className="space-y-2 text-sm">
            <p>
              <strong>1.</strong> Dirija-se à loja {order.storeName}
            </p>
            <p className="ml-4">{order.storeAddress}</p>
            <p>
              <strong>2.</strong> Mencione a referência: {order.id}
            </p>
            <p>
              <strong>3.</strong> Efetue o pagamento e levante o seu par
            </p>
            <p>
              <strong>4.</strong> A reserva é válida por 24 horas.
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-muted rounded-lg p-4">
        <Text as="h4" size="sm" className="mb-2 font-semibold">
          {t("paymentInstructions")}
        </Text>
        <div className="space-y-2 text-sm">
          <p>
            <strong>1.</strong> Para concluir a compra, efetue o pagamento para
            as seguintes coordenadas:
          </p>
          <div className="bg-background mt-2 rounded p-3">
            <div className="grid grid-cols-1 gap-1 text-xs">
              <div>
                <strong>Entidade:</strong> {PAYMENT_COORDINATES.entity}
              </div>
              <div>
                <strong>NIF:</strong> {PAYMENT_COORDINATES.nif}
              </div>
              <div>
                <strong>Banco:</strong> {PAYMENT_COORDINATES.bank}
              </div>
              <div>
                <strong>Conta:</strong> {PAYMENT_COORDINATES.account}
              </div>
              <div>
                <strong>IBAN:</strong> {PAYMENT_COORDINATES.iban}
              </div>
            </div>
          </div>
          <p>
            <strong>2.</strong> Envie o comprovativo para: {CONTACT_INFO.email}{" "}
            ou {CONTACT_INFO.phone}
          </p>
          <p>
            <strong>3.</strong> Após confirmação, receberá o recibo e a
            referência final de compra.
          </p>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">
            {t("orderDetails")} - {order.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Status and Basic Info */}
          <div className="flex flex-wrap items-center justify-between gap-4">
            <Badge
              className={cn(
                "text-xs font-semibold",
                statusColors[order.status],
              )}
            >
              {t(`status.${order.status}`)}
            </Badge>
            <div className="text-muted-foreground flex flex-wrap items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Calendar className="size-4" />
                {new Date(order.createdAt).toLocaleDateString()}
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div>
            <Text as="h3" size="lg" className="mb-3 font-semibold">
              {t("customerInfo")}
            </Text>
            <div className="grid grid-cols-1 gap-3 text-sm sm:grid-cols-2">
              <div>
                <strong>{t("name")}:</strong> {order.firstName} {order.lastName}
              </div>
              <div>
                <strong>{t("email")}:</strong> {order.email}
              </div>
              <div>
                <strong>{t("phone")}:</strong> {order.phone}
              </div>
            </div>
          </div>

          {/* Order Items */}
          <div>
            <Text as="h3" size="lg" className="mb-3 font-semibold">
              {t("orderItems")}
            </Text>
            <ul className="space-y-3">
              {order.items &&
                order.items.map((item, index) => (
                  <li
                    key={index}
                    className="flex items-center justify-between border-b pb-3 last:border-b-0"
                  >
                    <div className="flex-1">
                      <Text as="p" size="sm" className="font-medium">
                        {item.name}
                      </Text>
                      <Text as="p" size="xs" className="text-muted-foreground">
                        {t("size")}: {item.size.euSize} • {t("quantity")}:{" "}
                        {item.quantity}
                      </Text>
                    </div>
                    <Text as="p" size="sm" className="font-medium">
                      {formatKwanza(item.price * item.quantity)}
                    </Text>
                  </li>
                ))}
            </ul>

            <div className="mt-3 flex items-center justify-between border-t pt-3 font-semibold">
              <Text as="span" size="lg">
                {t("total")}:
              </Text>
              <Text as="span" size="lg">
                {formatKwanza(order.total)}
              </Text>
            </div>
          </div>

          {/* Store Information */}
          <div>
            <Text as="h3" size="lg" className="mb-3 font-semibold">
              {t("storeInfo")}
            </Text>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <MapPin className="size-4" />
                <div>
                  <div className="font-medium">{order.storeName}</div>
                  <div className="text-muted-foreground">
                    {order.storeAddress}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div>
            <Text as="h3" size="lg" className="mb-3 font-semibold">
              {t("paymentInfo")}
            </Text>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CreditCard className="size-4" />
                <Text as="span" size="sm">
                  {tPaymentMethods(order.paymentMethod)}
                </Text>
              </div>
              {getPaymentInstructions()}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default function OrdersList({ userId, className }: OrdersListProps) {
  const [orders, setOrders] = useState<OrderType[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<OrderType | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const t = useTranslations("Orders");
  const tPaymentMethods = useTranslations("CheckoutForm.paymentMethods");

  useEffect(() => {
    const fetchOrders = async () => {
      const fetchedOrders = await getUserOrdersById(userId);
      setOrders(fetchedOrders);
    };
    fetchOrders();
  }, [userId]);

  const handleViewDetails = (order: OrderType) => {
    setSelectedOrder(order);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedOrder(null);
  };

  if (orders.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Package className="text-muted-foreground mb-4 h-12 w-12" />
          <Text as="h3" size="lg" className="mb-2 font-semibold">
            {t("noOrders")}
          </Text>
          <Text as="p" size="sm" className="text-muted-foreground text-center">
            {t("noOrdersDescription")}
          </Text>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="text-xl">{t("yourOrders")}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-4">
        {orders.map((order) => (
          <Card key={order.id}>
            <CardHeader className="gap-4">
              <div className="flex flex-wrap items-center justify-between gap-2">
                <CardTitle className="text-base">
                  {t("orderNumber")}: {order.id}
                </CardTitle>
                <Badge
                  className={cn(
                    "text-xs font-semibold",
                    statusColors[order.status],
                  )}
                >
                  {t(`status.${order.status}`)}
                </Badge>
              </div>
              <div className="text-muted-foreground flex flex-wrap items-center gap-2 text-xs sm:text-sm">
                <div className="flex items-center gap-1">
                  <Calendar className="size-4" />
                  {new Date(order.createdAt).toLocaleDateString()}
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="size-4" />
                  {order.storeName}
                </div>
                <div className="flex items-center gap-1">
                  <CreditCard className="size-4" />
                  {tPaymentMethods(order.paymentMethod)}
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              <ul className="space-y-2">
                {order.items &&
                  order.items.map((item, index) => (
                    <li
                      key={index}
                      className="flex items-center gap-2 border-b py-2 last:border-b-0 max-sm:flex-col max-sm:items-start sm:justify-between"
                    >
                      <div className="flex-1">
                        <Text as="p" size="sm" className="font-medium">
                          {item.name}
                        </Text>
                        <Text
                          as="p"
                          size="xs"
                          className="text-muted-foreground"
                        >
                          {t("size")}: {item.size.euSize} • {t("quantity")}:{" "}
                          {item.quantity}
                        </Text>
                      </div>
                      <Text as="p" size="xs" className="font-medium">
                        {formatKwanza(item.price)}
                      </Text>
                    </li>
                  ))}
              </ul>

              <div className="flex items-center justify-between border-t pt-2 font-semibold">
                <Text as="span" size="sm">
                  {t("total")}:
                </Text>
                <Text as="span" size="sm">
                  {formatKwanza(order.total)}
                </Text>
              </div>

              <Text as="p" size="sm" className="text-secondary">
                {t("pickupLocation")}: {order.storeAddress}
              </Text>
            </CardContent>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
}
