import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Package, Calendar, MapPin, CreditCard } from "lucide-react";
import Text from "@/components/Text";
import { Badge } from "@/components/ui/badge";
import { format<PERSON>wan<PERSON> } from "@/lib/currency";
import { OrderType } from "@/app/[locale]/(order)/model";
import { getTranslations } from "next-intl/server";
import { getUserOrdersById } from "@/app/[locale]/(order)/actions";
import OrderRowActions from "@/components/OrderRowActions";

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
} satisfies Record<OrderType["status"], string>;

type OrdersListProps = {
  userId: string;
  className?: string;
};

export default async function OrdersList({
  userId,
  className,
}: OrdersListProps) {
  const orders = await getUserOrdersById(userId);
  const t = await getTranslations("Orders");
  const tPaymentMethods = await getTranslations("CheckoutForm.paymentMethods");

  if (orders.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Package className="text-muted-foreground mb-4 h-12 w-12" />
          <Text as="h3" size="lg" className="mb-2 font-semibold">
            {t("noOrders")}
          </Text>
          <Text as="p" size="sm" className="text-muted-foreground text-center">
            {t("noOrdersDescription")}
          </Text>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="text-xl">{t("yourOrders")}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-4">
        {orders.map((order) => (
          <Card key={order.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between gap-2">
                <div className="min-w-0 flex-1">
                  <CardTitle className="truncate text-base">
                    {t("orderNumber")}: {order.id}
                  </CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    className={cn(
                      "text-xs font-semibold",
                      statusColors[order.status],
                    )}
                  >
                    {t(`status.${order.status}`)}
                  </Badge>
                  <OrderRowActions order={order} />
                </div>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              <div className="grid grid-cols-1 gap-4 text-sm sm:grid-cols-3">
                <div className="flex items-center gap-2">
                  <Calendar className="text-muted-foreground size-4" />
                  <span>{new Date(order.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="text-muted-foreground size-4" />
                  <span className="truncate">{order.storeName}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CreditCard className="text-muted-foreground size-4" />
                  <span className="truncate">
                    {tPaymentMethods(order.paymentMethod)}
                  </span>
                </div>
              </div>

              <div className="mt-4 flex items-center justify-between border-t pt-4">
                <Text as="span" size="sm" className="text-muted-foreground">
                  {t("total")}:
                </Text>
                <Text as="span" size="lg" className="font-semibold">
                  {formatKwanza(order.total)}
                </Text>
              </div>
            </CardContent>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
}
