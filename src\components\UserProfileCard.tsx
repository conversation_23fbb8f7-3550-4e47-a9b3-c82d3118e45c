import { buttonVariants } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Link } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { Edit } from "lucide-react";
import { getTranslations } from "next-intl/server";
import { linksObj } from "@/app/links";
import Text from "@/components/Text";
import ProfileLogoutButton from "./ProfileLogoutButton";

type UserProfileCardProps = {
  className?: string;
  user: {
    name: string;
    email: string;
    phoneNumber?: string | null;
  };
};

export default async function UserProfileCard({
  className,
  user,
}: UserProfileCardProps) {
  const t = await getTranslations("Profile");

  const [firstName, lastName] = user.name?.split(" ") ?? ["", ""];

  const info = [
    { label: t("firstName"), value: firstName },
    { label: t("lastName"), value: lastName },
    { label: t("email"), value: user.email },
    { label: t("phone"), value: user.phoneNumber ?? "" },
  ];

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="">
        <CardTitle className="text-xl">{t("yourProfile")}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-6">
        <article className="grid gap-2">
          {info.map((item, index) => (
            <div key={index} className="flex justify-between">
              <Text
                as="span"
                size="sm"
                className="text-muted-foreground font-semibold"
              >
                {item.label}:
              </Text>
              <Text as="span" size="sm">
                {item.value}
              </Text>
            </div>
          ))}
        </article>

        <span className="flex flex-wrap gap-4">
          <Link
            className={cn(
              buttonVariants({ variant: "outline", size: "sm" }),
              "w-fit",
            )}
            href={linksObj.profileEdit.href}
          >
            <Edit className="h-4 w-4" />
            {t("editProfile")}
          </Link>
          <ProfileLogoutButton />
        </span>
      </CardContent>
    </Card>
  );
}
