"use client";
import LogoIcon from "@/assets/imgs/kurguen-icon.svg";
import { CartDialog } from "@/app/[locale]/(cart)/CartDialog";
import LanguageDropdown from "@/components/LanguageDropdown";
import { textVariants } from "@/components/Text";
import { Link } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { scroll } from "motion";
import { useTranslations } from "next-intl";
import { Fragment, useDeferredValue, useEffect, useState } from "react";
import UserIcon from "./UserIcon";

type Props = { className?: string };

const Header = ({ className }: Props) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const deferredIsScrolled = useDeferredValue(isScrolled);
  const t = useTranslations("Header");

  useEffect(() => {
    const cleanup = scroll((progress: number) => {
      setIsScrolled(progress > 0.05);
    });

    return cleanup;
  }, []);

  const navLinksBottom = [
    {
      label: t("navBottom.about"),
      href: "/#about",
    },
    {
      label: t("navBottom.shop"),
      href: "/#product",
    },
    {
      label: t("navBottom.contact"),
      href: "/#contact",
    },
  ];

  const navLinksTop = [
    {
      label: t("navTop.findStore"),
      href: "/#stores",
    },
    {
      Component: LanguageDropdown,
    },
    {
      Component: UserIcon,
    },
    {
      Component: CartDialog,
    },
  ];

  return (
    <div
      className={cn(
        "sticky top-0 z-50 w-full transition duration-300 ease-out",
        deferredIsScrolled
          ? "bg-background/80 shadow backdrop-blur-md"
          : "bg-white/20",
      )}
    >
      <header
        className={cn(
          "flex w-full items-center justify-between gap-5",
          className,
        )}
      >
        <Link href="/" className="flex">
          <LogoIcon
            className={cn(
              "h-auto w-8 transition-[width] duration-300 ease-out sm:w-12",
              deferredIsScrolled && "w-6 sm:w-8",
            )}
          />
        </Link>
        <section
          className={cn(
            "flex flex-col items-end gap-0 transition-[gap] duration-300 ease-out sm:gap-4",
            deferredIsScrolled && "gap-0 sm:gap-2",
          )}
        >
          <nav className="font-sans-2 flex flex-wrap items-center justify-end gap-x-2 gap-y-1 font-light text-white lowercase sm:gap-x-4">
            {navLinksTop.map((link, index) => (
              <Fragment key={index}>
                {link.Component ? (
                  <link.Component />
                ) : (
                  <Link
                    href={link.href}
                    className={cn(
                      textVariants({ size: "sm" }),
                      "hocus:text-accent flex items-center gap-1 hover:underline",
                    )}
                  >
                    {/* {link.Icon && (
                      <link.Icon
                        className="size-4 sm:size-5"
                        strokeWidth={1.5}
                      />
                    )} */}
                    {link.label && link.label}
                  </Link>
                )}
                {index !== navLinksTop.length - 1 && <span>|</span>}
              </Fragment>
            ))}
          </nav>
          <nav
            className={cn("text-accent flex gap-4 tracking-wider lowercase")}
          >
            {navLinksBottom.map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className={cn(
                  textVariants({ size: "xl" }),
                  "flex items-center gap-1 transition-all duration-300 ease-out hover:underline",
                  deferredIsScrolled && "text-lg sm:text-2xl",
                )}
              >
                {link.label}
              </Link>
            ))}
          </nav>
        </section>
      </header>
    </div>
  );
};

export default Header;
